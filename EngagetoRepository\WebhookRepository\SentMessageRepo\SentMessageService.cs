﻿using Engageto.Hubs;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.WebhookContracts.Client;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoRepository.WebhookRepository.WhatsAppBusinessEndpointUrl;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Text;
using EngagetoContracts.Services;
using EngagetoContracts.GeneralContracts;
using Microsoft.Extensions.Logging;

namespace EngagetoRepository.WebhookRepository.SentMessageRepo
{
    public class SentMessageService : IWhatsAppBusinessClient
    {
        private readonly JsonSerializer _serializer = new JsonSerializer();
        private readonly HttpClient _httpClient = new HttpClient();
        private readonly IDbContextFactory<ApplicationDbContext> _appDbContext;
        private readonly IHttpClientFactory _httpClientFactory;
        private IHubContext<MessageHub, IMessageHubClient> messageHub;
        private IConfiguration configuration;
        private readonly IWebhookService _webhookService;
        private readonly IEnvironmentService _environmentService;
        private readonly ILogger<SentMessageService> _logger;

        public SentMessageService(IConfiguration config,
            IHubContext<MessageHub, IMessageHubClient> _messageHub,
            IDbContextFactory<ApplicationDbContext> appDbContext,
            IHttpClientFactory httpClient,
            IWebhookService webhookService,
            IEnvironmentService environmentService,
            ILogger<SentMessageService> logger)
        {
            _appDbContext = appDbContext;
            _httpClientFactory = httpClient;
            messageHub = _messageHub;
            configuration = config;
            _webhookService = webhookService;
            _environmentService = environmentService;
            _logger = logger;
            //whatsAppReceiveNotification = data;
        }

        public async Task MarkMessageAsReadAsync(MarkMessageRequest markMessage, Guid BusinessId)
        {
            using var context = _appDbContext.CreateDbContext();
            var MetaPhoneNumber
               = context.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.PhoneNumberID;
            if (MetaPhoneNumber == null)
            {
                MetaPhoneNumber = configuration["Facebook:PhoneNumberId"];
            }
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.MarkMessageAsRead.Replace("{{Phone-Number-ID}}", MetaPhoneNumber);
            await WhatsAppBusinessPostAsync(markMessage, formattedWhatsAppEndpoint, BusinessId);
        }

        public async Task<WhatsAppResponse> SendMessageAsync(TextMessageRequest textMessage, Guid BusinessId, String? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {
            using var context = _appDbContext.CreateDbContext();
            var MetaPhoneNumber
              = context.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.PhoneNumberID;
            if (MetaPhoneNumber == null)
            {
                MetaPhoneNumber = configuration["Facebook:PhoneNumberId"];
            }
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", MetaPhoneNumber);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(textMessage, formattedWhatsAppEndpoint, BusinessId, SentBy, MessageType);
        }
        public async Task<WhatsAppResponse> SendMessageAsync(ImageMessageRequest Message, Guid BusinessId, string? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {

            var MetaPhoneNumber
               = _appDbContext.CreateDbContext().BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.PhoneNumberID;
            if (MetaPhoneNumber == null)
            {
                MetaPhoneNumber = configuration["Facebook:PhoneNumberId"];
            }
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", MetaPhoneNumber);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(Message, formattedWhatsAppEndpoint, BusinessId, SentBy, MessageType);
        }
        public async Task<WhatsAppResponse> SendMessageAsync(AudioMessageRequest Message, Guid BusinessId, string? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {
            var MetaPhoneNumber = _appDbContext.CreateDbContext().BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.PhoneNumberID;
            if (MetaPhoneNumber == null)
            {
                MetaPhoneNumber = configuration["Facebook:PhoneNumberId"];
            }
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", MetaPhoneNumber);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(Message, formattedWhatsAppEndpoint, BusinessId, SentBy, MessageType);
        }

        public async Task<WhatsAppResponse> SendMessageAsync(VideoMessageRequest Message, Guid BusinessId, string? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {
            var MetaPhoneNumber = _appDbContext.CreateDbContext().BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.PhoneNumberID;
            if (MetaPhoneNumber == null)
            {
                MetaPhoneNumber = configuration["Facebook:PhoneNumberId"];
            }
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", MetaPhoneNumber);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(Message, formattedWhatsAppEndpoint, BusinessId, SentBy, MessageType);
        }
        public async Task<WhatsAppResponse> SendMessageAsync(DocumentMessageRequest Message, Guid BusinessId, string? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {
            var MetaPhoneNumber = _appDbContext.CreateDbContext().BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.PhoneNumberID;
            if (MetaPhoneNumber == null)
            {
                MetaPhoneNumber = configuration["Facebook:PhoneNumberId"];
            }
            var formattedWhatsAppEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.SendMessage.Replace("{{Phone-Number-ID}}", MetaPhoneNumber);
            return await WhatsAppBusinessPostAsync<WhatsAppResponse>(Message, formattedWhatsAppEndpoint, BusinessId, SentBy, MessageType);
        }
        private async Task<WhatsAppResponse> WhatsAppBusinessPostAsync<T>(TextMessageRequest whatsAppDto,
            string whatsAppBusinessEndpoint, Guid BusinessId, string? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {
            using var context = _appDbContext.CreateDbContext();
            var MetaDetails = (await context.BusinessDetailsMetas.FirstOrDefaultAsync(m => m.BusinessId == BusinessId.ToString()))?.Token;
            string AccessToken = string.Empty;
            if (MetaDetails == null)
            {
                AccessToken = configuration["Facebook:AccessToken"];
            }
            else
            {
                AccessToken = MetaDetails;
            }
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);
            WhatsAppResponse result = new();
            result.Contacts = new List<EngagetoEntities.Dtos.MetaDto.WAContact>();
            result.Contacts.Add(new EngagetoEntities.Dtos.MetaDto.WAContact { WaId = whatsAppDto.To, Input = whatsAppDto.To });
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var Id = await InsertSentTextMessage(result, whatsAppDto.Text.Body, whatsAppDto.Context?.MessageId,
                BusinessId, SentBy, MessageType);
            List<ConversationDto> conversations = new List<ConversationDto>();
            List<Conversations> query = new List<Conversations>();

            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                if (Id != Guid.Empty)
                {
                    var sendingMessage = await context.Conversations.FirstOrDefaultAsync(m => m.Id == Id);
                    if (sendingMessage != null)
                    {
                        sendingMessage.Status = ConvStatus.sent;
                        sendingMessage.WhatsAppMessageId = result.Messages[0].Id;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();


                        if (Obj.Entity.ReplyId != null)
                        {
                            query = await context.Conversations.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId).ToListAsync();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));
                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            else
            {
                if (Id != Guid.Empty)
                {
                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {
                        sendingMessage.Status = ConvStatus.failed;
                        var Obj = context.Conversations.Update(sendingMessage);
                        await context.SaveChangesAsync();


                        if (Obj.Entity.ReplyId != null)
                        {
                            query = await context.Conversations.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId).ToListAsync();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));

                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            return result;
        }
        private async Task<WhatsAppResponse> WhatsAppBusinessPostAsync<T>(ImageMessageRequest whatsAppDto,
            string whatsAppBusinessEndpoint, Guid BusinessId, string? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {
            using var context = _appDbContext.CreateDbContext();
            var MetaDetails = context.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.Token;
            string AccessToken = string.Empty;
            if (MetaDetails == null)
            {
                AccessToken = configuration["Facebook:AccessToken"];
            }
            else
            {
                AccessToken = MetaDetails;
            }
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);
            WhatsAppResponse result = new();
            result.Contacts = new List<EngagetoEntities.Dtos.MetaDto.WAContact>();
            result.Contacts.Add(new WAContact { WaId = whatsAppDto.To });
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var Id = await InsertSentMediaMessage(result, whatsAppDto.Image.Link, whatsAppDto.Image.Caption, whatsAppDto.Context?.MessageId, BusinessId, SentBy, MessageType);
            List<ConversationDto> conversations = new List<ConversationDto>();
            List<Conversations> query = new List<Conversations>();

            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);

                if (Id != Guid.Empty)
                {


                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {
                        sendingMessage.Status = ConvStatus.sent;
                        sendingMessage.WhatsAppMessageId = result.Messages[0].Id;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();


                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));

                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            else
            {
                if (Id != Guid.Empty)
                {


                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {


                        sendingMessage.Status = ConvStatus.failed;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();


                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));

                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            return result;
        }
        private async Task<WhatsAppResponse> WhatsAppBusinessPostAsync<T>(AudioMessageRequest whatsAppDto, string whatsAppBusinessEndpoint,
            Guid BusinessId, string? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {
            using var context = _appDbContext.CreateDbContext();
            var MetaDetails = context.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.Token;
            string AccessToken = string.Empty;
            if (MetaDetails == null)
            {
                AccessToken = configuration["Facebook:AccessToken"];
            }
            else
            {
                AccessToken = MetaDetails;
            }
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);
            WhatsAppResponse result = new();
            result.Contacts = new List<WAContact>();

            result.Contacts.Add(new WAContact { WaId = whatsAppDto.To });
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var Id = await InsertSentMediaMessage(result, whatsAppDto.Audio.Link, "", whatsAppDto.Context?.MessageId, BusinessId, SentBy, MessageType);
            List<ConversationDto> conversations = new List<ConversationDto>();
            List<Conversations> query = new List<Conversations>();
            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                if (Id != Guid.Empty)
                {


                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {
                        sendingMessage.Status = ConvStatus.sent;
                        sendingMessage.WhatsAppMessageId = result.Messages[0].Id;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();

                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));

                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            else
            {
                if (Id != Guid.Empty)
                {


                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {


                        sendingMessage.Status = ConvStatus.failed;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();


                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));
                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }
                        }
                    }
                }
            }
            return result;
        }
        private async Task<WhatsAppResponse> WhatsAppBusinessPostAsync<T>(VideoMessageRequest whatsAppDto, string whatsAppBusinessEndpoint,
            Guid BusinessId, string? SentBy = null
           , MessageType MessageType = MessageType.Normal)
        {
            using var context = _appDbContext.CreateDbContext();
            var MetaDetails = context.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.Token;
            string AccessToken = string.Empty;
            if (MetaDetails == null)
            {
                AccessToken = configuration["Facebook:AccessToken"];
            }
            else
            {
                AccessToken = MetaDetails;
            }
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);
            WhatsAppResponse result = new();
            result.Contacts = new List<WAContact>();
            result.Contacts.Add(new WAContact { WaId = whatsAppDto.To });
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var Id = await InsertSentMediaMessage(result, whatsAppDto.Video.Link, whatsAppDto.Video.Caption,
                whatsAppDto.Context?.MessageId, BusinessId, SentBy, MessageType);
            List<ConversationDto> conversations = new List<ConversationDto>();
            List<Conversations> query = new List<Conversations>();
            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                if (Id != Guid.Empty)
                {
                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {
                        sendingMessage.Status = ConvStatus.sent;
                        sendingMessage.WhatsAppMessageId = result.Messages[0].Id;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();

                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));
                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            else
            {
                if (Id != Guid.Empty)
                {


                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {


                        sendingMessage.Status = ConvStatus.failed;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();
                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));
                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            return result;
        }
        private async Task<WhatsAppResponse> WhatsAppBusinessPostAsync<T>(DocumentMessageRequest whatsAppDto, string whatsAppBusinessEndpoint,
            Guid BusinessId, string? SentBy = null
            , MessageType MessageType = MessageType.Normal)
        {
            using var context = _appDbContext.CreateDbContext();
            var MetaDetails = context.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == BusinessId.ToString())?.Token;
            string AccessToken = string.Empty;
            if (MetaDetails == null)
            {
                AccessToken = configuration["Facebook:AccessToken"];
            }
            else
            {
                AccessToken = MetaDetails;
            }
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);
            WhatsAppResponse result = new();
            result.Contacts = new List<WAContact>();
            result.Contacts.Add(new WAContact { WaId = whatsAppDto.To });
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var Id = await InsertSentMediaMessage(result, whatsAppDto.Document.Link, whatsAppDto.Document.Caption, whatsAppDto.Context?.MessageId, BusinessId, SentBy, MessageType);
            List<ConversationDto> conversations = new List<ConversationDto>();
            List<Conversations> query = new List<Conversations>();

            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                if (Id != Guid.Empty)
                {
                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {
                        sendingMessage.Status = ConvStatus.sent;
                        sendingMessage.WhatsAppMessageId = result.Messages[0].Id;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();

                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));
                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            else
            {
                if (Id != Guid.Empty)
                {


                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {
                        sendingMessage.Status = ConvStatus.failed;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();

                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));

                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            return result;
        }

        public async Task<WhatsAppResponse> WhatsAppBusinessPostAsync(AutoReply.Rootobject whatsAppDto, Guid BusinessId, string? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {
            using var context = _appDbContext.CreateDbContext();
            var businessDetails = await context.BusinessDetailsMetas.FirstOrDefaultAsync(m => m.BusinessId == BusinessId.ToString());
            var whatsAppBusinessEndpoint = WhatsAppBusinessRequestEndpoint.BaseAddress + WhatsAppBusinessRequestEndpoint.MarkMessageAsRead.Replace("{{Phone-Number-ID}}",
                businessDetails?.PhoneNumberID);

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", businessDetails?.Token);
            WhatsAppResponse result = new();
            result.Contacts = new List<WAContact>();
            result.Contacts.Add(new WAContact { WaId = whatsAppDto.to });
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var Id = await InsertSentMessage(result, whatsAppDto, BusinessId, SentBy, MessageType);
            List<ConversationDto> conversations = new List<ConversationDto>();
            List<Conversations> query = new List<Conversations>();

            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                result = _serializer.Deserialize<WhatsAppResponse>(jsonReader);
                if (Id != Guid.Empty)
                {
                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {
                        sendingMessage.Status = ConvStatus.sent;
                        sendingMessage.WhatsAppMessageId = result.Messages[0].Id;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();

                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));
                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            else
            {
                if (Id != Guid.Empty)
                {
                    var sendingMessage = context.Conversations.FirstOrDefault(m => m.Id == Id);
                    if (sendingMessage != null)
                    {
                        sendingMessage.Status = ConvStatus.failed;
                        var Obj = context.Conversations.Update(sendingMessage);
                        context.SaveChanges();

                        if (Obj.Entity.ReplyId != null)
                        {
                            query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                        }
                        EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                        conversations.Add(messages.Message(Obj.Entity, query));

                        var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                        if (data != null)
                        {
                            var UserIds = data.Select(m => m.Id).ToList();
                            foreach (var UserId in UserIds)
                            {
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                            }

                        }
                    }
                }
            }
            return result;
        }
        private async Task<Guid> InsertSentMessage(WhatsAppResponse response, AutoReply.Rootobject AutoReply, Guid BusinessId, string? SentBy = null,
            MessageType MessageType = MessageType.Normal)
        {
            try
            {
                using var context = _appDbContext.CreateDbContext();
                Conversations sentMessage = new Conversations();
                string toContact = response.Contacts[0].WaId;
                var Contact = await context.Contacts.FirstOrDefaultAsync(m => (m.BusinessId.ToString().ToLower() == BusinessId.ToString().ToLower()) && string.Concat(m.CountryCode, m.Contact).Replace("+", "") == toContact.Replace("+", ""));
                sentMessage.WhatsAppMessageId = "";
                sentMessage.MessageType = MessageType;
                sentMessage.UserId = SentBy;
                sentMessage.From = BusinessId.ToString();
                sentMessage.To = toContact;
                sentMessage.BusinessId = BusinessId;
                sentMessage.ContactId = Contact?.ContactId ?? Guid.Empty;
                sentMessage.Status = ConvStatus.sending;
                sentMessage.TextMessage = AutoReply?.interactive?.body?.text;
                var settings = new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore // Ignore null values
                };

                // Serialize the object with the custom settings
                sentMessage.Action = JsonConvert.SerializeObject(AutoReply?.interactive?.action, settings);

                sentMessage.ReplyId = null;
                sentMessage.CreatedAt = DateTime.UtcNow;
                string dateTimeString = sentMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                // Parse the string back to a DateTime object
                DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                // Assign parsed DateTime back to sentMessage.CreatedAt
                sentMessage.CreatedAt = parsedDateTime;
                List<ConversationDto> conversations = new List<ConversationDto>();

                var Obj = context.Conversations.Add(sentMessage);
                context.SaveChanges();
                List<Conversations> query = new List<Conversations>();
                if (Obj.Entity.ReplyId != null)
                {
                    query = await context.Conversations.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId).ToListAsync();
                }

                EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                conversations.Add(messages.Message(Obj.Entity, query));

                await Response(Contact.ContactId);

                // Convert DateTime to string without timezone specifier
                var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                if (data != null)
                {
                    var UserIds = data.Select(m => m.Id).ToList();
                    foreach (var UserId in UserIds)
                    {
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                    }

                }
                return Obj.Entity.Id;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private async Task<Guid> InsertSentTextMessage(WhatsAppResponse response, string TextBody, string? ReplyId,
            Guid BusinessId, string? SentBy = null, MessageType MessageType = MessageType.Normal)
        {
            try
            {
                using var context = _appDbContext.CreateDbContext();
                Conversations sentMessage = new Conversations();
                string toContact = response.Contacts[0].WaId;
                var Contact = await context.Contacts.FirstOrDefaultAsync(m => (m.BusinessId.ToString().ToLower() == BusinessId.ToString().ToLower()) && ((m.CountryCode + m.Contact).Replace("+", "") == toContact.Replace("+", "")) && m.IsActive  && !m.IsDeleted);
                sentMessage.MessageType = MessageType;
                sentMessage.UserId = SentBy;
                sentMessage.From = BusinessId.ToString();
                sentMessage.WhatsAppMessageId = "";
                sentMessage.To = toContact;
                sentMessage.BusinessId = BusinessId;
                sentMessage.ContactId = Contact?.ContactId ?? Guid.Empty;
                sentMessage.Status = ConvStatus.sent;
                sentMessage.TextMessage = TextBody;
                sentMessage.ReplyId = ReplyId;

                sentMessage.CreatedAt = DateTime.UtcNow;
                string dateTimeString = sentMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                // Parse the string back to a DateTime object
                DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                // Assign parsed DateTime back to sentMessage.CreatedAt
                sentMessage.CreatedAt = parsedDateTime;
                List<ConversationDto> conversations = new List<ConversationDto>();
                var Obj = await context.Conversations.AddAsync(sentMessage);
                // update last message time into contacts
                if (Contact != null)
                {
                    Contact.LastMessageAt = DateTime.UtcNow;
                   await Response(Contact.ContactId);
                }
                await context.SaveChangesAsync();
                List<Conversations> query = new List<Conversations>();
                if (Obj.Entity.ReplyId != null)
                {
                    query = await context.Conversations.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId).ToListAsync();
                }

                EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                conversations.Add(messages.Message(Obj.Entity, query));


                // Convert DateTime to string without timezone specifier
                var data = await context.Users.Where(m => m.CompanyId == BusinessId.ToString()).ToListAsync();
                if (data != null)
                {
                    var UserIds = data.Select(m => m.Id).ToList();
                    foreach (var UserId in UserIds)
                    {
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                    }

                }
                return Obj.Entity.Id;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private async Task<Guid> InsertSentMediaMessage(WhatsAppResponse response, string Link, string Caption, string ReplyId, Guid BusinessId,
            string? SentBy = null, MessageType MessageType = MessageType.Normal)
        {
            try
            {
                using var context = _appDbContext.CreateDbContext();
                var client = _httpClientFactory.CreateClient();
                var result = await client.GetAsync(Link);
                var contentType = result.Content.Headers.ContentType?.MediaType;
                Conversations sentMessage = new Conversations();
                string toContact = response.Contacts[0].WaId;
                var Contact = await context.Contacts.FirstOrDefaultAsync(m => (m.BusinessId.ToString().ToLower() == BusinessId.ToString().ToLower()) && ((m.CountryCode + m.Contact).Replace("+", "") == toContact.Replace("+", "")) && m.IsActive && !m.IsDeleted);
                sentMessage.WhatsAppMessageId = "";
                sentMessage.MessageType = MessageType;
                sentMessage.UserId = SentBy;
                sentMessage.From = BusinessId.ToString();
                sentMessage.To = toContact;
                sentMessage.BusinessId = BusinessId;
                sentMessage.ContactId = Contact?.ContactId ?? Guid.Empty;
                sentMessage.Status = ConvStatus.sending;
                sentMessage.MediaMimeType = contentType;
                sentMessage.MediaUrl = Link;
                sentMessage.MediaCaption = Caption;
                sentMessage.CreatedAt = DateTime.UtcNow;
                string dateTimeString = sentMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                // Parse the string back to a DateTime object
                DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                // Assign parsed DateTime back to sentMessage.CreatedAt
                sentMessage.CreatedAt = parsedDateTime;



                List<ConversationDto> conversations = new List<ConversationDto>();
                var Obj = context.Conversations.Add(sentMessage);
                if (Contact != null)
                {
                    await Response(Contact.ContactId);
                    Contact.LastMessageAt = DateTime.UtcNow;
                }
                context.SaveChanges();
                List<Conversations> query = new List<Conversations>();
                if (Obj.Entity.ReplyId != null)
                {
                    query = context.Conversations?.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId)?.ToList();
                }

                EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                conversations.Add(messages.Message(Obj.Entity, query));

                var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                if (data != null)
                {
                    var UserIds = data.Select(m => m.Id).ToList();
                    foreach (var UserId in UserIds)
                    {
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                    }

                }
                return Obj.Entity.Id;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        private async Task<Guid> InsertSentMediaMessage(WhatsAppResponse response, WhatsAppDocument document, string ReplyId,
            Guid BusinessId, string? SentBy = null, MessageType MessageType = MessageType.Normal)
        {
            try
            {
                using var context = _appDbContext.CreateDbContext();
                var client = _httpClientFactory.CreateClient();
                var result = await client.GetAsync(document.Link);
                var contentType = result.Content.Headers.ContentType?.MediaType;
                Conversations sentMessage = new Conversations();
                string toContact = response.Contacts[0].WaId;
                var Contact = await context.Contacts.FirstOrDefaultAsync(m => (m.BusinessId.ToString().ToLower() == BusinessId.ToString().ToLower()) && ((m.CountryCode + m.Contact).Replace("+", "") == toContact.Replace("+", "")) && m.IsActive && !m.IsDeleted);

                sentMessage.WhatsAppMessageId = "";

                sentMessage.MessageType = MessageType;
                sentMessage.UserId = SentBy;
                sentMessage.From = BusinessId.ToString();
                sentMessage.To = toContact;
                sentMessage.Status = ConvStatus.sending;
                sentMessage.MediaMimeType = contentType;
                sentMessage.BusinessId = BusinessId;
                sentMessage.ContactId = Contact?.ContactId ?? Guid.Empty;
                sentMessage.MediaUrl = document.Link;
                sentMessage.MediaCaption = document.Caption;
                sentMessage.ReplyId = ReplyId;
                sentMessage.MediaFileName = document.FileName;

                sentMessage.CreatedAt = DateTime.UtcNow;
                List<ConversationDto> conversations = new List<ConversationDto>();
                string dateTimeString = sentMessage.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffffff");

                // Parse the string back to a DateTime object
                DateTime parsedDateTime = DateTime.ParseExact(dateTimeString, "yyyy-MM-ddTHH:mm:ss.fffffff", System.Globalization.CultureInfo.InvariantCulture);

                // Assign parsed DateTime back to sentMessage.CreatedAt
                sentMessage.CreatedAt = parsedDateTime;
                var Obj = await context.Conversations.AddAsync(sentMessage);
                context.SaveChanges();
                List<Conversations> query = new List<Conversations>();
                if (Obj.Entity.ReplyId != null)
                {
                    query = await context.Conversations.Where(m => m.WhatsAppMessageId == Obj.Entity.ReplyId).ToListAsync();
                }
                EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                conversations.Add(messages.Message(Obj.Entity, query));
                await Response(Contact.ContactId);
                var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
                if (data != null)
                {
                    var UserIds = data.Select(m => m.Id).ToList();
                    foreach (var UserId in UserIds)
                    {
                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();

                        await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                    }

                }
                return Obj.Entity.Id;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        private async Task<WhatsAppResponse> WhatsAppBusinessPostAsync(MarkMessageRequest whatsAppDto,
            string whatsAppBusinessEndpoint, Guid BusinessId)
        {
            using var context = _appDbContext.CreateDbContext();
            var MetaDetails = (await context.BusinessDetailsMetas.FirstOrDefaultAsync(m => m.BusinessId == BusinessId.ToString()))?.Token;
            string AccessToken = string.Empty;
            if (MetaDetails == null)
            {
                AccessToken = configuration["Facebook:AccessToken"];
            }
            else
            {
                AccessToken = MetaDetails;
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken);
            WhatsAppResponse result = new();
            string json = JsonConvert.SerializeObject(whatsAppDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(whatsAppBusinessEndpoint, content);

            var convData = await context.Conversations.Where(m => m.WhatsAppMessageId == whatsAppDto.MessageId).FirstOrDefaultAsync();
            if (convData != null) 
            { 
                if (convData.Status == ConvStatus.received || convData.Status == ConvStatus.receivedRead)
                {
                    convData.Status = ConvStatus.receivedRead;
                }
                context.Conversations.Update(convData); // Update all messages at once
                await context.SaveChangesAsync();
            }
            //foreach (var conversation in Data)
            //{
            //    var messagesToUpdate = await context.Conversations
            //      .Where(m => (m.To == conversation.To && m.From == conversation.From) && (m.CreatedAt <= conversation.CreatedAt))
            //      .ToListAsync(); // Load messages into memory

            //    foreach (var message in messagesToUpdate)
            //    {
            //        if (message.Status == ConvStatus.received)
            //        {
            //            message.Status = ConvStatus.receivedRead;
            //        }
            //        else
            //        {
            //            message.Status = ConvStatus.read;
            //        }

            //        //await _webhookService.SaveWebhookEventsAsync(BusinessId.ToString(), "UpdatedEndUserMessage", message);
            //    }
            //    var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
            //    if (data != null)
            //    {
            //        var UserIds = data.Select(m => m.Id).ToList();
            //        foreach (var UserId in UserIds)
            //        {
            //            await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
            //        }

            //    }
            //    context.Conversations.UpdateRange(messagesToUpdate); // Update all messages at once
            //    context.SaveChanges();
            //}
            var data = context.Users.Where(m => m.CompanyId == BusinessId.ToString());
            if (data != null)
            {
                var UserIds = data.Select(m => m.Id).ToList();
                foreach (var UserId in UserIds)
                {
                    await messageHub.Clients.Groups(BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                }
            }
            return result;
        }

        #region Delay Response Delete Object If We Got Response From Client
        public async Task Response(Guid ContactId)
        {
            using var context = _appDbContext.CreateDbContext();
            var Contact = await context.Contacts.Where(m => m.ContactId == ContactId).FirstOrDefaultAsync();

            if (Contact.DelayResponseJobID != null && await IsJobScheduledAsync(Contact.DelayResponseJobID))
            {
                await DeleteJobAsync(Contact.DelayResponseJobID);
                Contact.DelayResponseJobID = null;
                context.Contacts.Update(Contact);
                await context.SaveChangesAsync();
            }
        }

        #region Azure Function Helper Methods

        /// <summary>
        /// Deletes a job using Azure Function TerminateJob endpoint
        /// </summary>
        private async Task DeleteJobAsync(string jobId)
        {
            try
            {
                var httpClient = _httpClientFactory.CreateClient();
                var terminateJobUrl = _environmentService.IsDevelopment
                    ? configuration["FunctionSettings:Dev_TerminateJobUrl"]
                    : configuration["FunctionSettings:Prod_TerminateJobUrl"];

                var requestBody = new { JobId = jobId };
                var json = JsonConvert.SerializeObject(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await httpClient.PostAsync(terminateJobUrl, content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Failed to delete job {jobId}. Status: {response.StatusCode}, Content: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking the main flow
                _logger.LogError(ex, "Error deleting job {JobId}: {ErrorMessage}", jobId, ex.Message);
            }
        }
        private async Task<bool> IsJobScheduledAsync(string jobId)
        {
            try
            {
                var httpClient = _httpClientFactory.CreateClient();
                var statusUrl = _environmentService.IsDevelopment
                    ? configuration["FunctionSettings:Dev_JobStatusUrl"]?.Replace("{jobId}", jobId)
                    : configuration["FunctionSettings:Prod_JobStatusUrl"]?.Replace("{jobId}", jobId);

                var response = await httpClient.GetAsync(statusUrl);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var statusResponse = JsonConvert.DeserializeObject<dynamic>(content);
                    var runtimeStatus = statusResponse?.runtimeStatus?.ToString();

                    // Return true if status is "Pending" (equivalent to "Scheduled")
                    return string.Equals(runtimeStatus, "Running", StringComparison.OrdinalIgnoreCase);
                }

                return false;
            }
            catch (Exception ex)
            {
                // Log error and return false to be safe
                _logger.LogError(ex, "Error checking job status for {JobId}: {ErrorMessage}", jobId, ex.Message);
                return false;
            }
        }

        #endregion

        #endregion
    }
}

